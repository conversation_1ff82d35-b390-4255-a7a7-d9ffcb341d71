#!/usr/bin/env python3
"""
测试Higgs Audio V2的情感表达能力和语音合成延迟
"""

import os
import time
import torch
import torchaudio
from loguru import logger
from boson_multimodal.audio_processing.higgs_audio_tokenizer import load_higgs_audio_tokenizer
from boson_multimodal.serve.serve_engine import HiggsAudioServeEngine, HiggsAudioResponse
from boson_multimodal.data_types import ChatMLSample, Message, AudioContent

# 模型路径
MODEL_PATH = "./models/bosonai/higgs-audio-v2-generation-3B-base"
AUDIO_TOKENIZER_PATH = "./models/bosonai/higgs-audio-v2-tokenizer"
REF_AUDIO = "zh_vo_Main_Linaxita_2_3_35_6"

# 不同情感的测试文本
EMOTION_TESTS = {
    "happy": {
        "text": "今天真是太棒了！我收到了心仪已久的工作offer，简直不敢相信这是真的！我要和朋友们一起庆祝！",
        "scene": "说话者心情愉悦兴奋，语调上扬，语速稍快，充满喜悦和激动的情感。"
    },
    "sad": {
        "text": "看着窗外的雨滴，我想起了那些逝去的美好时光。有些人一旦离开，就再也回不来了。",
        "scene": "说话者情绪低落，语调沉重，语速缓慢，带有淡淡的忧伤和怀念。"
    },
    "angry": {
        "text": "这简直太过分了！我已经忍无可忍了！为什么总是这样对待我？这不公平！",
        "scene": "说话者情绪激动愤怒，语调强烈，语速较快，声音有力度，表达强烈不满。"
    },
    "surprised": {
        "text": "什么？！你说什么？这怎么可能？我完全没有想到会是这样的结果！",
        "scene": "说话者非常惊讶，语调起伏很大，语速变化明显，充满意外和震惊。"
    },
    "calm": {
        "text": "生活就像一条平静的河流，缓缓地向前流淌。我们需要学会在忙碌中寻找内心的宁静。",
        "scene": "说话者语调平和稳定，语速适中，声音温和，传达出宁静祥和的情感。"
    },
    "excited": {
        "text": "哇！这个消息太令人兴奋了！我们终于要实现梦想了！快点，我们马上就开始行动吧！",
        "scene": "说话者极度兴奋，语调高昂，语速很快，充满活力和热情。"
    }
}

def create_scene_prompt_file(emotion, scene_desc):
    """创建场景提示文件"""
    scene_file = f"examples/scene_prompts/emotion_{emotion}.txt"
    with open(scene_file, 'w', encoding='utf-8') as f:
        f.write(f"音频录制于安静的室内环境。{scene_desc}")
    return scene_file

def load_models():
    """加载模型，返回加载时间"""
    logger.info("开始加载模型...")
    start_time = time.time()
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    logger.info(f"使用设备: {device}")
    
    # 加载音频tokenizer
    audio_tokenizer = load_higgs_audio_tokenizer(AUDIO_TOKENIZER_PATH, device=device)
    
    # 加载服务引擎
    serve_engine = HiggsAudioServeEngine(MODEL_PATH, AUDIO_TOKENIZER_PATH, device=device)
    
    load_time = time.time() - start_time
    logger.info(f"模型加载完成，耗时: {load_time:.2f}秒")
    
    return serve_engine, load_time

def test_emotion_synthesis(serve_engine, emotion, text, scene_desc):
    """测试单个情感的语音合成"""
    logger.info(f"\n=== 测试情感: {emotion.upper()} ===")
    logger.info(f"测试文本: {text}")
    
    # 创建场景提示文件
    scene_file = create_scene_prompt_file(emotion, scene_desc)
    
    # 准备消息
    system_prompt = f"Generate audio following instruction.\n\n<|scene_desc_start|>\n{scene_desc}\n<|scene_desc_end|>"
    
    # 加载参考音频
    ref_audio_path = f"examples/voice_prompts/{REF_AUDIO}.wav"
    ref_text_path = f"examples/voice_prompts/{REF_AUDIO}.txt"
    
    with open(ref_text_path, 'r', encoding='utf-8') as f:
        ref_text = f.read().strip()
    
    messages = [
        Message(role="system", content=system_prompt),
        Message(role="user", content=ref_text),
        Message(role="assistant", content=AudioContent(audio_url=ref_audio_path)),
        Message(role="user", content=text)
    ]
    
    # 开始计时（不包括模型加载时间）
    synthesis_start = time.time()
    
    # 生成音频
    output: HiggsAudioResponse = serve_engine.generate(
        chat_ml_sample=ChatMLSample(messages=messages),
        max_new_tokens=1024,
        temperature=1.0,  # 使用默认温度
        top_p=0.95,
        top_k=50,
        stop_strings=["<|end_of_text|>", "<|eot_id|>"],
    )
    
    synthesis_time = time.time() - synthesis_start
    
    # 保存音频文件
    output_file = f"emotion_test_{emotion}.wav"
    torchaudio.save(output_file, torch.from_numpy(output.audio)[None, :], output.sampling_rate)
    
    # 计算音频时长
    audio_duration = len(output.audio) / output.sampling_rate
    
    logger.info(f"合成完成:")
    logger.info(f"  - 合成延迟: {synthesis_time:.2f}秒")
    logger.info(f"  - 音频时长: {audio_duration:.2f}秒")
    logger.info(f"  - 实时率: {audio_duration/synthesis_time:.2f}x")
    logger.info(f"  - 输出文件: {output_file}")
    
    return {
        'emotion': emotion,
        'synthesis_time': synthesis_time,
        'audio_duration': audio_duration,
        'real_time_factor': audio_duration / synthesis_time,
        'output_file': output_file
    }

def main():
    """主函数"""
    logger.info("开始测试Higgs Audio V2的情感表达能力和合成延迟")
    
    # 加载模型
    serve_engine, load_time = load_models()
    
    # 测试结果
    results = []
    
    # 测试每种情感
    for emotion, config in EMOTION_TESTS.items():
        try:
            result = test_emotion_synthesis(
                serve_engine, 
                emotion, 
                config['text'], 
                config['scene']
            )
            results.append(result)
        except Exception as e:
            logger.error(f"测试情感 {emotion} 时出错: {e}")
    
    # 输出总结报告
    logger.info("\n" + "="*60)
    logger.info("测试总结报告")
    logger.info("="*60)
    logger.info(f"模型加载时间: {load_time:.2f}秒")
    logger.info(f"测试情感数量: {len(results)}")
    
    if results:
        avg_synthesis_time = sum(r['synthesis_time'] for r in results) / len(results)
        avg_audio_duration = sum(r['audio_duration'] for r in results) / len(results)
        avg_rtf = sum(r['real_time_factor'] for r in results) / len(results)
        
        logger.info(f"\n平均性能指标:")
        logger.info(f"  - 平均合成延迟: {avg_synthesis_time:.2f}秒")
        logger.info(f"  - 平均音频时长: {avg_audio_duration:.2f}秒")
        logger.info(f"  - 平均实时率: {avg_rtf:.2f}x")
        
        logger.info(f"\n各情感详细结果:")
        for result in results:
            logger.info(f"  {result['emotion']:>10}: "
                       f"延迟={result['synthesis_time']:.2f}s, "
                       f"时长={result['audio_duration']:.2f}s, "
                       f"RTF={result['real_time_factor']:.2f}x")
    
    logger.info(f"\n生成的音频文件:")
    for result in results:
        logger.info(f"  - {result['output_file']} ({result['emotion']})")
    
    logger.info("\n测试完成！请听取生成的音频文件来评估情感表达效果。")

if __name__ == "__main__":
    main()
