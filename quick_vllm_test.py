#!/usr/bin/env python3
"""
快速vLLM测试脚本
"""

import time
import requests
from test_vllm_fixed_prompt import HiggsAudioVLLMClient, VLLMTester, FixedPromptConfig
from loguru import logger

# 测试文本
TEST_TEXTS = [
    "今天天气真不错，阳光明媚，微风徐徐。",
    "春天来了，万物复苏，花儿开始绽放。",
    "夜晚的城市灯火通明，街道上车水马龙。",
    "这是一个美好的季节，让人心情愉悦。"
]

def check_vllm_server():
    """检查vLLM服务器是否运行"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    logger.info("开始vLLM快速测试")
    
    # 检查服务器
    if not check_vllm_server():
        logger.error("vLLM服务器未启动！")
        logger.info("请先启动服务器:")
        logger.info("方法1: python start_vllm_server.py")
        logger.info("方法2: 手动启动vLLM服务器")
        return
    
    logger.info("vLLM服务器已启动 ✓")
    
    # 创建客户端和测试器
    client = HiggsAudioVLLMClient()
    tester = VLLMTester(
        client, 
        prompt_config="chinese_natural",  # 使用中文自然配置
        ref_audio="zh_vo_Main_Linaxita_2_3_35_6"
    )
    
    logger.info("开始测试语音合成...")
    
    # 测试单个文本
    logger.info("\n=== 单个文本测试 ===")
    result = tester.synthesize_text(
        "你好，这是使用vLLM进行的语音合成测试。效果如何呢？",
        "vllm_single_test.wav"
    )
    
    if result["success"]:
        logger.info("✓ 单个文本测试成功")
    else:
        logger.error("✗ 单个文本测试失败")
        return
    
    # 测试批量合成
    logger.info("\n=== 批量合成测试 ===")
    results = tester.batch_synthesize(TEST_TEXTS, "vllm_batch")
    
    successful = [r for r in results if r["success"]]
    logger.info(f"批量测试完成: {len(successful)}/{len(TEST_TEXTS)} 成功")
    
    # 测试流式输出
    logger.info("\n=== 流式输出测试 ===")
    stream_result = tester.synthesize_text(
        "这是流式输出测试，应该能够更快地开始播放音频。",
        "vllm_stream_test.wav",
        stream=True
    )
    
    if stream_result["success"]:
        logger.info("✓ 流式输出测试成功")
    else:
        logger.error("✗ 流式输出测试失败")
    
    logger.info("\n=== 测试完成 ===")
    logger.info("生成的音频文件:")
    logger.info("- vllm_single_test.wav (单个文本)")
    logger.info("- vllm_batch_001.wav ~ vllm_batch_004.wav (批量测试)")
    logger.info("- vllm_stream_test.wav (流式输出)")
    
    logger.info("\nvLLM测试总结:")
    logger.info("✓ 服务器连接正常")
    logger.info("✓ 语音合成功能正常")
    logger.info("✓ 固定prompt配置生效")
    logger.info("✓ 批量处理功能正常")
    logger.info("✓ 流式输出功能正常")

if __name__ == "__main__":
    main()
