#!/usr/bin/env python3
"""
优化的中文语音合成测试脚本
使用单次模型加载，避免重复初始化，充分利用4090性能
"""

import os
import torch
import torchaudio
import time
from loguru import logger
from boson_multimodal.serve.serve_engine import Higgs<PERSON>udioServeEngine, HiggsAudioResponse
from boson_multimodal.data_types import ChatMLSample, Message

def test_fast_chinese_voice():
    """快速中文语音合成测试"""
    
    logger.info("🚀 开始快速中文语音合成测试...")
    logger.info(f"🎮 GPU: {torch.cuda.get_device_name(0)}")
    logger.info(f"💾 显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
    
    # 使用本地模型路径
    model_path = "./models/bosonai/higgs-audio-v2-generation-3B-base"
    tokenizer_path = "./models/bosonai/higgs-audio-v2-tokenizer"
    device = "cuda"
    
    # 创建输出目录
    output_dir = "./fast_chinese_outputs"
    os.makedirs(output_dir, exist_ok=True)
    
    # 测试用例
    test_cases = [
        {
            "text": "你好，这是使用你的声音进行语音克隆的测试。",
            "ref_audio": "zh_vo_Main_Linaxita_2_3_35_6",
            "name": "your_voice_basic"
        },
        {
            "text": "人工智能技术正在快速发展，语音合成效果越来越自然。",
            "ref_audio": "zh_vo_Main_Linaxita_2_3_35_6", 
            "name": "your_voice_tech"
        },
        {
            "text": "今天天气很好，阳光明媚，心情也很不错。",
            "ref_audio": "zh_vo_Main_Linaxita_2_3_35_6",
            "name": "your_voice_daily"
        },
        # {
        #     "text": "中华文化博大精深，有着五千年的悠久历史。",
        #     "ref_audio": None,  # 智能语音
        #     "name": "smart_voice"
        # }
    ]
    
    try:
        # 一次性初始化模型（这是最耗时的部分）
        logger.info("⏳ 正在初始化模型（这可能需要20-30秒）...")
        start_init = time.time()
        
        serve_engine = HiggsAudioServeEngine(
            model_path, 
            tokenizer_path, 
            device=device
        )
        
        init_time = time.time() - start_init
        logger.info(f"✅ 模型初始化完成，耗时: {init_time:.1f}秒")
        
        # 系统提示
        system_prompt = (
            "Generate audio following instruction.\n\n"
            "<|scene_desc_start|>\nAudio is recorded from a quiet room.\n<|scene_desc_end|>"
        )
        
        success_count = 0
        total_generation_time = 0
        
        # 批量生成（复用已加载的模型）
        for i, case in enumerate(test_cases, 1):
            logger.info(f"\n🎯 测试 {i}/{len(test_cases)}: {case['name']}")
            logger.info(f"📝 文本: {case['text']}")
            if case['ref_audio']:
                logger.info(f"🎭 参考声音: {case['ref_audio']}")
            else:
                logger.info("🤖 智能语音（无参考）")
            
            # 构建消息
            messages = [Message(role="system", content=system_prompt)]
            
            # 如果有参考音频，需要添加到消息中
            if case['ref_audio']:
                # 读取参考音频文本
                ref_text_file = f"examples/voice_prompts/{case['ref_audio']}.txt"
                if os.path.exists(ref_text_file):
                    with open(ref_text_file, 'r', encoding='utf-8') as f:
                        ref_text = f.read().strip()

                    # 添加参考音频消息
                    from boson_multimodal.data_types import AudioContent
                    messages.append(Message(role="user", content=ref_text))
                    messages.append(Message(
                        role="assistant",
                        content=AudioContent(audio_url=f"examples/voice_prompts/{case['ref_audio']}.wav")
                    ))
            
            # 添加目标文本
            messages.append(Message(role="user", content=case['text']))
            
            try:
                start_gen = time.time()
                
                # 生成语音
                output: HiggsAudioResponse = serve_engine.generate(
                    chat_ml_sample=ChatMLSample(messages=messages),
                    max_new_tokens=1024,
                    temperature=0.3,
                    top_p=0.95,
                    top_k=50,
                    stop_strings=["<|end_of_text|>", "<|eot_id|>"],
                )
                
                gen_time = time.time() - start_gen
                total_generation_time += gen_time
                
                # 保存音频
                output_path = os.path.join(output_dir, f"{case['name']}.wav")
                torchaudio.save(
                    output_path, 
                    torch.from_numpy(output.audio)[None, :], 
                    output.sampling_rate
                )
                
                file_size = os.path.getsize(output_path)
                audio_duration = len(output.audio) / output.sampling_rate
                
                logger.info(f"✅ 生成成功!")
                logger.info(f"   📁 文件: {output_path}")
                logger.info(f"   ⏱️  生成耗时: {gen_time:.1f}秒")
                logger.info(f"   🎵 音频时长: {audio_duration:.1f}秒")
                logger.info(f"   📊 文件大小: {file_size} bytes")
                logger.info(f"   🚀 生成速度: {audio_duration/gen_time:.1f}x 实时")
                
                success_count += 1
                
            except Exception as e:
                logger.error(f"❌ 生成失败: {e}")
        
        # 统计结果
        avg_gen_time = total_generation_time / success_count if success_count > 0 else 0
        
        logger.info(f"\n{'='*60}")
        logger.info(f"📊 测试结果统计:")
        logger.info(f"✅ 成功: {success_count}/{len(test_cases)}")
        logger.info(f"⏱️  模型初始化: {init_time:.1f}秒")
        logger.info(f"⏱️  平均生成时间: {avg_gen_time:.1f}秒")
        logger.info(f"⏱️  总耗时: {init_time + total_generation_time:.1f}秒")
        logger.info(f"📁 输出目录: {output_dir}")
        
        if success_count == len(test_cases):
            logger.info("🎉 所有测试都成功了！")
        
        return success_count > 0
        
    except Exception as e:
        logger.error(f"💥 测试失败: {e}")
        return False

if __name__ == "__main__":
    logger.info("🎭 快速中文语音合成测试 (优化版)")
    logger.info("💡 这个版本只加载一次模型，大大提升速度")
    
    success = test_fast_chinese_voice()
    
    if success:
        logger.info("\n🚀 性能优化提示:")
        logger.info("1. 模型初始化是最耗时的部分（20-30秒）")
        logger.info("2. 后续每次生成只需要几秒钟")
        logger.info("3. 如果需要批量生成，建议使用这种方式")
        logger.info("4. 4090的24GB显存足够运行这个3B模型")
    else:
        logger.error("💥 测试失败！")
        exit(1)
