#!/usr/bin/env python3
"""
测试不同system_prompt对语音合成自然度的影响
"""

import os
import time
import torch
import torchaudio
from loguru import logger
from boson_multimodal.audio_processing.higgs_audio_tokenizer import load_higgs_audio_tokenizer
from boson_multimodal.serve.serve_engine import HiggsAudioServeEngine, HiggsAudioResponse
from boson_multimodal.data_types import ChatMLSample, Message, AudioContent

# 模型路径
MODEL_PATH = "./models/bosonai/higgs-audio-v2-generation-3B-base"
AUDIO_TOKENIZER_PATH = "./models/bosonai/higgs-audio-v2-tokenizer"
REF_AUDIO = "zh_vo_Main_Linaxita_2_3_35_6"

# 测试文本
TEST_TEXT = "春暖花开的季节里，我们一家人决定去郊外踏青。孩子们在草地上奔跑嬉戏，大人们在树荫下聊天休息，这样的时光总是让人感到格外珍贵和美好。"

# 不同的system prompt配置
SYSTEM_PROMPT_CONFIGS = {
    "default": {
        "scene": "Audio is recorded from a quiet room.",
        "description": "默认场景提示（英文）"
    },
    "chinese_basic": {
        "scene": "音频录制于安静的室内环境。",
        "description": "基础中文场景提示"
    },
    "chinese_detailed": {
        "scene": "音频录制于安静的室内环境，说话者语调自然流畅，情感丰富，语速适中，发音清晰标准。",
        "description": "详细中文场景提示"
    },
    "chinese_emotional": {
        "scene": "音频录制于温馨的家庭环境中，说话者心情愉悦放松，语调温和亲切，带有淡淡的幸福感，语速自然舒缓。",
        "description": "情感化中文场景提示"
    },
    "chinese_professional": {
        "scene": "音频录制于专业录音室环境，音质清晰无杂音，说话者发音标准，语调平稳，语速适中，适合正式场合使用。",
        "description": "专业化中文场景提示"
    }
}

def load_models():
    """加载模型"""
    logger.info("加载模型中...")
    device = "cuda" if torch.cuda.is_available() else "cpu"
    serve_engine = HiggsAudioServeEngine(MODEL_PATH, AUDIO_TOKENIZER_PATH, device=device)
    logger.info("模型加载完成")
    return serve_engine

def test_system_prompt(serve_engine, config_name, scene_desc, description):
    """测试单个system prompt配置"""
    logger.info(f"\n=== 测试配置: {config_name.upper()} ===")
    logger.info(f"描述: {description}")
    logger.info(f"场景提示: {scene_desc}")
    
    # 准备消息
    system_prompt = f"Generate audio following instruction.\n\n<|scene_desc_start|>\n{scene_desc}\n<|scene_desc_end|>"
    
    # 加载参考音频
    ref_audio_path = f"examples/voice_prompts/{REF_AUDIO}.wav"
    ref_text_path = f"examples/voice_prompts/{REF_AUDIO}.txt"
    
    with open(ref_text_path, 'r', encoding='utf-8') as f:
        ref_text = f.read().strip()
    
    messages = [
        Message(role="system", content=system_prompt),
        Message(role="user", content=ref_text),
        Message(role="assistant", content=AudioContent(audio_url=ref_audio_path)),
        Message(role="user", content=TEST_TEXT)
    ]
    
    # 开始计时
    synthesis_start = time.time()
    
    # 生成音频
    output: HiggsAudioResponse = serve_engine.generate(
        chat_ml_sample=ChatMLSample(messages=messages),
        max_new_tokens=1024,
        temperature=1.0,
        top_p=0.95,
        top_k=50,
        stop_strings=["<|end_of_text|>", "<|eot_id|>"],
    )
    
    synthesis_time = time.time() - synthesis_start
    
    # 保存音频文件
    output_file = f"system_prompt_test_{config_name}.wav"
    torchaudio.save(output_file, torch.from_numpy(output.audio)[None, :], output.sampling_rate)
    
    # 计算音频时长
    audio_duration = len(output.audio) / output.sampling_rate
    
    logger.info(f"合成完成:")
    logger.info(f"  - 合成延迟: {synthesis_time:.2f}秒")
    logger.info(f"  - 音频时长: {audio_duration:.2f}秒")
    logger.info(f"  - 实时率: {audio_duration/synthesis_time:.2f}x")
    logger.info(f"  - 输出文件: {output_file}")
    
    return {
        'config_name': config_name,
        'description': description,
        'synthesis_time': synthesis_time,
        'audio_duration': audio_duration,
        'real_time_factor': audio_duration / synthesis_time,
        'output_file': output_file
    }

def main():
    """主函数"""
    logger.info("开始测试不同system_prompt对语音合成的影响")
    logger.info(f"测试文本: {TEST_TEXT}")
    
    # 加载模型
    serve_engine = load_models()
    
    # 测试结果
    results = []
    
    # 测试每种配置
    for config_name, config in SYSTEM_PROMPT_CONFIGS.items():
        try:
            result = test_system_prompt(
                serve_engine, 
                config_name, 
                config['scene'], 
                config['description']
            )
            results.append(result)
        except Exception as e:
            logger.error(f"测试配置 {config_name} 时出错: {e}")
    
    # 输出总结报告
    logger.info("\n" + "="*80)
    logger.info("System Prompt 对比测试总结报告")
    logger.info("="*80)
    logger.info(f"测试配置数量: {len(results)}")
    logger.info(f"测试文本: {TEST_TEXT}")
    
    if results:
        avg_synthesis_time = sum(r['synthesis_time'] for r in results) / len(results)
        avg_audio_duration = sum(r['audio_duration'] for r in results) / len(results)
        avg_rtf = sum(r['real_time_factor'] for r in results) / len(results)
        
        logger.info(f"\n平均性能指标:")
        logger.info(f"  - 平均合成延迟: {avg_synthesis_time:.2f}秒")
        logger.info(f"  - 平均音频时长: {avg_audio_duration:.2f}秒")
        logger.info(f"  - 平均实时率: {avg_rtf:.2f}x")
        
        logger.info(f"\n各配置详细结果:")
        for result in results:
            logger.info(f"  {result['config_name']:>18}: "
                       f"延迟={result['synthesis_time']:.2f}s, "
                       f"时长={result['audio_duration']:.2f}s, "
                       f"RTF={result['real_time_factor']:.2f}x")
            logger.info(f"  {' '*18}  描述: {result['description']}")
    
    logger.info(f"\n生成的音频文件:")
    for result in results:
        logger.info(f"  - {result['output_file']} ({result['description']})")
    
    logger.info("\n测试完成！")
    logger.info("建议:")
    logger.info("1. 听取不同配置生成的音频文件，比较语音的自然度和表现力")
    logger.info("2. 中文场景提示通常比英文提示更适合中文语音合成")
    logger.info("3. 详细的场景描述可能会提高语音的表现力和自然度")
    logger.info("4. 根据具体应用场景选择合适的system prompt配置")

if __name__ == "__main__":
    main()
