#!/usr/bin/env python3
"""
简洁的中文语音合成测试脚本
直接使用官方的generation.py，避免重复加载模型
"""

import os
import subprocess
from loguru import logger

def run_generation(transcript, ref_audio=None, output_name="test", seed=12345, temperature=0.3):
    """运行语音生成，使用官方推荐的方式"""
    
    output_dir = "./chinese_outputs"
    os.makedirs(output_dir, exist_ok=True)
    
    output_file = os.path.join(output_dir, f"{output_name}.wav")
    
    # 基础命令（按照官方文档格式）
    cmd = [
        "./conda_env/bin/python", "examples/generation.py",
        "--transcript", transcript,
        "--seed", str(seed),
        "--temperature", str(temperature),
        "--out_path", output_file
    ]
    
    # 添加参考音频（如果指定）
    if ref_audio:
        cmd.extend(["--ref_audio", ref_audio])
    
    logger.info(f"🎵 生成: {output_name}")
    logger.info(f"📝 文本: {transcript}")
    if ref_audio:
        logger.info(f"🎭 参考声音: {ref_audio}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            file_size = os.path.getsize(output_file) if os.path.exists(output_file) else 0
            logger.info(f"✅ 成功: {output_file} ({file_size} bytes)")
            return True
        else:
            logger.error(f"❌ 失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("⏰ 超时")
        return False
    except Exception as e:
        logger.error(f"💥 错误: {e}")
        return False

def main():
    """主测试函数"""
    
    logger.info("🇨🇳 开始中文语音合成测试...")
    
    # 测试用例
    tests = [
        # 1. 智能语音（无参考音频）
        {
            "transcript": "你好，这是一个中文语音合成测试。",
            "ref_audio": None,
            "output_name": "chinese_smart_voice",
            "description": "智能语音"
        },
        
        # 2. 使用你的自定义声音
        {
            "transcript": "你好，这是使用你的声音进行语音克隆的测试。",
            "ref_audio": "zh_vo_Main_Linaxita_2_3_35_6",
            "output_name": "chinese_your_voice",
            "description": "你的自定义声音"
        },
        
        # 3. 使用马保国声音
        {
            "transcript": "人工智能技术正在快速发展，语音合成效果越来越好。",
            "ref_audio": "mabaoguo",
            "output_name": "chinese_mabaoguo_voice",
            "description": "马保国声音"
        },
        
        # 4. 使用四川男性声音
        {
            "transcript": "今天天气很好，阳光明媚，适合出去走走。",
            "ref_audio": "zh_man_sichuan",
            "output_name": "chinese_sichuan_voice",
            "description": "四川男性声音"
        },
        
        # 5. 长文本测试（使用你的声音）
        {
            "transcript": "中华文化博大精深，有着五千年的悠久历史。从古代的诗词歌赋到现代的科技创新，中华民族始终在历史的长河中闪闪发光。",
            "ref_audio": "zh_vo_Main_Linaxita_2_3_35_6",
            "output_name": "chinese_long_text",
            "description": "长文本测试"
        }
    ]
    
    success_count = 0
    
    for i, test in enumerate(tests, 1):
        logger.info(f"\n{'='*50}")
        logger.info(f"测试 {i}/5: {test['description']}")
        logger.info(f"{'='*50}")
        
        success = run_generation(
            transcript=test["transcript"],
            ref_audio=test["ref_audio"],
            output_name=test["output_name"],
            seed=12345,
            temperature=0.3
        )
        
        if success:
            success_count += 1
    
    # 结果统计
    logger.info(f"\n{'='*60}")
    logger.info(f"📊 测试结果: {success_count}/{len(tests)} 成功")
    logger.info(f"📁 音频文件保存在: ./chinese_outputs/")
    
    if success_count == len(tests):
        logger.info("🎉 所有测试都成功了！")
    elif success_count > 0:
        logger.info("⚠️ 部分测试成功")
    else:
        logger.error("❌ 所有测试都失败了")
    
    return success_count > 0

if __name__ == "__main__":
    success = main()
    
    if success:
        logger.info("\n💡 使用提示:")
        logger.info("1. 直接使用官方脚本进行语音合成:")
        logger.info("   python3 examples/generation.py --transcript '你的文本' --ref_audio zh_vo_Main_Linaxita_2_3_35_6 --out_path output.wav")
        logger.info("2. 不指定ref_audio可以使用智能语音")
        logger.info("3. 可以调整--temperature参数控制变化程度(0.1-1.0)")
        logger.info("4. 使用--seed参数可以获得可重复的结果")
    else:
        exit(1)
