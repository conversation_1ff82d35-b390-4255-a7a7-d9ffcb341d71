# Higgs Audio V2 vLLM 使用指南

## 概述

本指南介绍如何使用vLLM来部署和测试Higgs Audio V2，实现高性能的语音合成服务。vLLM提供了更高的吞吐量和更好的并发处理能力。

## 功能特点

- **高性能**: vLLM优化的推理引擎，支持更高的吞吐量
- **固定Prompt**: 预配置多种场景提示，无需每次设置
- **流式输出**: 支持流式音频生成，降低首音延迟
- **批量处理**: 支持批量文本合成
- **OpenAI兼容**: 使用标准OpenAI API接口

## 安装依赖

首先确保安装了vLLM：

```bash
pip install vllm
pip install openai  # OpenAI客户端
```

## 使用方法

### 1. 启动vLLM服务器

#### 方法1: 使用提供的启动脚本
```bash
python start_vllm_server.py
```

#### 方法2: 手动启动
```bash
python -m vllm.entrypoints.openai.api_server \
  --served-model-name "higgs-audio-v2-generation-3B-base" \
  --model "./models/bosonai/higgs-audio-v2-generation-3B-base" \
  --audio-tokenizer-type "./models/bosonai/higgs-audio-v2-tokenizer" \
  --limit-mm-per-prompt audio=50 \
  --max-model-len 8192 \
  --port 8000 \
  --gpu-memory-utilization 0.8 \
  --disable-mm-preprocessor-cache \
  --trust-remote-code
```

服务器启动后，可以通过以下地址访问：
- API服务: http://localhost:8000
- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

### 2. 快速测试

运行快速测试脚本：
```bash
python quick_vllm_test.py
```

这将测试：
- 单个文本合成
- 批量文本合成
- 流式输出功能

### 3. 使用固定Prompt配置

#### 查看可用配置
```bash
python test_vllm_fixed_prompt.py --list-configs
```

可用配置：
- `default`: 默认配置（英文）
- `chinese_natural`: 中文自然配置
- `chinese_emotional`: 中文情感配置
- `chinese_professional`: 中文专业配置

#### 单个文本合成
```bash
# 使用默认配置
python test_vllm_fixed_prompt.py --text "你好，这是测试文本。"

# 使用指定配置
python test_vllm_fixed_prompt.py \
  --prompt-config chinese_emotional \
  --text "今天心情真好，阳光明媚的日子让人感到愉悦。" \
  --output happy_test.wav
```

#### 批量文本合成
```bash
# 从文件读取多行文本进行批量合成
python test_vllm_fixed_prompt.py \
  --text-file batch_texts.txt \
  --prompt-config chinese_natural \
  --output batch_output
```

#### 流式输出
```bash
# 使用流式输出，降低首音延迟
python test_vllm_fixed_prompt.py \
  --text "这是流式输出测试。" \
  --stream \
  --output stream_test.wav
```

#### 交互式模式
```bash
# 进入交互式模式，可以连续输入文本进行合成
python test_vllm_fixed_prompt.py --prompt-config chinese_natural
```

### 4. 使用不同参考音频

```bash
# 使用不同的参考音频进行语音克隆
python test_vllm_fixed_prompt.py \
  --ref-audio zh_vo_Main_Linaxita_2_3_35_6 \
  --text "使用指定音色进行语音合成。"
```

## 性能对比

### vLLM vs 直接推理

| 指标 | 直接推理 | vLLM |
|------|----------|------|
| 吞吐量 | ~100 tokens/s | ~600-1500 tokens/s |
| 并发支持 | 有限 | 优秀 |
| 内存效率 | 一般 | 优化 |
| 首音延迟 | 3-7秒 | 2-5秒 |
| 流式支持 | 无 | 支持 |

### 硬件性能参考

- **A100 40GB**: ~1500 tokens/s (约60秒音频/秒)
- **RTX 4090 24GB**: ~600 tokens/s (约24秒音频/秒)
- **RTX 3090 24GB**: ~400 tokens/s (约16秒音频/秒)

## 固定Prompt配置详解

### 配置说明

1. **default**: 
   - 英文场景提示
   - 适合英文语音合成
   - 基础配置

2. **chinese_natural**:
   - 中文自然语调
   - 适合日常对话
   - 语调流畅自然

3. **chinese_emotional**:
   - 中文情感表达
   - 适合情感丰富的内容
   - 语调温和亲切

4. **chinese_professional**:
   - 中文专业配置
   - 适合正式场合
   - 发音标准清晰

### 自定义配置

可以在`test_vllm_fixed_prompt.py`中的`FixedPromptConfig.SYSTEM_PROMPTS`添加自定义配置：

```python
"my_custom": {
    "name": "我的自定义配置",
    "prompt": "Generate audio following instruction.\n\n<|scene_desc_start|>\n你的自定义场景描述\n<|scene_desc_end|>"
}
```

## API使用示例

### Python客户端

```python
from openai import OpenAI
import base64

client = OpenAI(
    api_key="EMPTY",
    base_url="http://localhost:8000/v1"
)

# 语音合成
response = client.chat.completions.create(
    model="higgs-audio-v2-generation-3B-base",
    messages=[
        {"role": "system", "content": "Generate audio following instruction.\n\n<|scene_desc_start|>\n音频录制于安静的室内环境。\n<|scene_desc_end|>"},
        {"role": "user", "content": "你好，这是测试文本。"}
    ],
    modalities=["text", "audio"],
    temperature=1.0
)

# 保存音频
audio_data = base64.b64decode(response.choices[0].message.audio.data)
with open("output.wav", "wb") as f:
    f.write(audio_data)
```

### cURL示例

```bash
curl -X POST "http://localhost:8000/v1/audio/speech" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "higgs-audio-v2-generation-3B-base",
    "voice": "zh_vo_Main_Linaxita_2_3_35_6",
    "input": "你好，这是使用cURL的测试。",
    "response_format": "wav"
  }' \
  --output test_curl.wav
```

## 故障排除

### 常见问题

1. **服务器启动失败**
   - 检查GPU内存是否足够
   - 确认模型文件路径正确
   - 检查端口8000是否被占用

2. **合成失败**
   - 检查参考音频文件是否存在
   - 确认文本编码为UTF-8
   - 检查服务器日志

3. **性能问题**
   - 调整`--gpu-memory-utilization`参数
   - 使用流式输出降低延迟
   - 考虑批量处理提高吞吐量

### 日志查看

服务器日志会显示详细的错误信息，有助于诊断问题。

## 总结

vLLM版本提供了更高性能的语音合成服务，特别适合：

- 需要高吞吐量的应用
- 多用户并发访问
- 实时语音合成需求
- 批量文本处理

通过固定Prompt配置，可以简化使用流程，提高开发效率。
