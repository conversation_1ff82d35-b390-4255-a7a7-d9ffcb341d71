#!/usr/bin/env python3
"""
中文语音克隆测试脚本
专门测试Higgs Audio V2的中文语音克隆功能
"""

import os
import sys
import subprocess
from loguru import logger

def test_chinese_voice_cloning():
    """测试中文语音克隆功能"""
    
    # 创建输出目录
    output_dir = "./chinese_voice_cloning_outputs"
    os.makedirs(output_dir, exist_ok=True)
    
    logger.info("🇨🇳 开始中文语音克隆测试...")
    
    # 中文测试文本
    test_texts = [
        "你好，这是一个中文语音克隆测试。我希望生成的语音能够模仿参考音频的声音特征。",
        "人工智能技术正在快速发展，语音合成技术也越来越成熟。",
        "今天天气很好，阳光明媚，适合出去走走。",
        "中华文化博大精深，有着五千年的悠久历史。",
        "科技改变生活，让我们的世界变得更加美好。"
    ]
    
    # 可用的中文参考声音
    chinese_voices = [
        ("mabaoguo", "马保国声音"),
        ("zh_man_sichuan", "四川男性声音")
    ]
    
    success_count = 0
    total_tests = len(test_texts) * len(chinese_voices)
    
    for voice_name, voice_desc in chinese_voices:
        logger.info(f"\n=== 测试 {voice_desc} ({voice_name}) ===")
        
        for i, text in enumerate(test_texts):
            logger.info(f"\n测试文本 {i+1}: {text}")
            
            output_file = os.path.join(output_dir, f"chinese_clone_{voice_name}_text_{i+1}.wav")
            
            # 构建命令
            cmd = [
                "./conda_env/bin/python", "examples/generation.py",
                "--model_path", "./models/bosonai/higgs-audio-v2-generation-3B-base",
                "--audio_tokenizer", "./models/bosonai/higgs-audio-v2-tokenizer",
                "--transcript", text,
                "--ref_audio", voice_name,
                "--temperature", "0.3",  # 使用较低温度保持稳定性
                "--device", "cuda",
                "--out_path", output_file
            ]
            
            try:
                logger.info(f"正在生成语音...")
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    file_size = os.path.getsize(output_file) if os.path.exists(output_file) else 0
                    logger.info(f"✅ 生成成功: {output_file} ({file_size} bytes)")
                    success_count += 1
                else:
                    logger.error(f"❌ 生成失败:")
                    logger.error(f"stderr: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                logger.error(f"⏰ 生成超时")
            except Exception as e:
                logger.error(f"💥 生成出错: {e}")
    
    # 测试结果统计
    logger.info(f"\n" + "="*60)
    logger.info(f"📊 中文语音克隆测试结果:")
    logger.info(f"成功: {success_count}/{total_tests}")
    logger.info(f"成功率: {success_count/total_tests*100:.1f}%")
    
    if success_count > 0:
        logger.info(f"✅ 中文语音克隆测试完成！")
        logger.info(f"📁 音频文件保存在: {output_dir}")
        return True
    else:
        logger.error("❌ 所有中文语音克隆测试都失败了")
        return False

def test_chinese_voice_comparison():
    """对比测试：同一文本使用不同中文声音"""
    
    logger.info("\n🔄 开始中文声音对比测试...")
    
    output_dir = "./chinese_voice_cloning_outputs"
    os.makedirs(output_dir, exist_ok=True)
    
    # 用于对比的测试文本
    comparison_text = "欢迎使用Higgs Audio V2语音合成系统，这是一个非常先进的人工智能语音技术。"
    
    chinese_voices = [
        ("mabaoguo", "马保国声音"),
        ("zh_man_sichuan", "四川男性声音")
    ]
    
    success_count = 0
    
    for voice_name, voice_desc in chinese_voices:
        logger.info(f"\n生成 {voice_desc} 版本...")
        
        output_file = os.path.join(output_dir, f"comparison_{voice_name}.wav")
        
        cmd = [
            "./conda_env/bin/python", "examples/generation.py",
            "--model_path", "./models/bosonai/higgs-audio-v2-generation-3B-base",
            "--audio_tokenizer", "./models/bosonai/higgs-audio-v2-tokenizer",
            "--transcript", comparison_text,
            "--ref_audio", voice_name,
            "--temperature", "0.3",
            "--device", "cuda",
            "--out_path", output_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                file_size = os.path.getsize(output_file) if os.path.exists(output_file) else 0
                logger.info(f"✅ {voice_desc} 生成成功: {output_file} ({file_size} bytes)")
                success_count += 1
            else:
                logger.error(f"❌ {voice_desc} 生成失败: {result.stderr}")
                
        except Exception as e:
            logger.error(f"💥 {voice_desc} 生成出错: {e}")
    
    logger.info(f"\n对比测试完成，成功: {success_count}/{len(chinese_voices)}")
    return success_count > 0

def show_usage_instructions():
    """显示如何添加自定义中文参考音频的说明"""
    
    logger.info("\n" + "="*60)
    logger.info("📝 如何添加自定义中文参考音频:")
    logger.info("="*60)
    logger.info("1. 准备音频文件:")
    logger.info("   - 格式: WAV文件，建议采样率16kHz或24kHz")
    logger.info("   - 长度: 3-10秒，包含清晰的中文语音")
    logger.info("   - 质量: 无背景噪音，语音清晰")
    logger.info("")
    logger.info("2. 准备对应的文本文件:")
    logger.info("   - 创建与音频文件同名的.txt文件")
    logger.info("   - 内容为音频中说话的准确文本")
    logger.info("")
    logger.info("3. 文件放置位置:")
    logger.info("   - 音频文件: examples/voice_prompts/your_voice_name.wav")
    logger.info("   - 文本文件: examples/voice_prompts/your_voice_name.txt")
    logger.info("")
    logger.info("4. 使用方法:")
    logger.info("   - 在测试脚本中使用 --ref_audio your_voice_name")
    logger.info("")
    logger.info("示例:")
    logger.info("   examples/voice_prompts/my_chinese_voice.wav")
    logger.info("   examples/voice_prompts/my_chinese_voice.txt")
    logger.info("   然后使用: --ref_audio my_chinese_voice")

if __name__ == "__main__":
    logger.info("🎭 开始中文语音克隆专项测试...")
    
    # 显示使用说明
    show_usage_instructions()
    
    # 中文语音克隆测试
    cloning_success = test_chinese_voice_cloning()
    
    # 中文声音对比测试
    comparison_success = test_chinese_voice_comparison()
    
    if cloning_success or comparison_success:
        logger.info("\n🎉 中文语音克隆测试完成！")
        logger.info("📁 所有音频文件保存在: ./chinese_voice_cloning_outputs/")
        logger.info("\n💡 提示: 你可以播放生成的音频文件来评估克隆效果")
    else:
        logger.error("\n💥 所有中文语音克隆测试都失败了！")
        sys.exit(1)
