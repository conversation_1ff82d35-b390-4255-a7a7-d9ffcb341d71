#!/usr/bin/env python3
"""
使用Docker运行vLLM服务器并测试语音合成
"""

import os
import time
import subprocess
import requests
import signal
import sys
from loguru import logger
from test_vllm_fixed_prompt import HiggsAudioVLLMClient, VLLMTester, FixedPromptConfig

class DockerVLLMManager:
    """Docker vLLM服务器管理器"""
    
    def __init__(self, port=8000):
        self.port = port
        self.container_name = f"higgs-audio-vllm-{port}"
        self.process = None
        
    def check_docker_available(self):
        """检查Docker是否可用"""
        try:
            result = subprocess.run(["docker", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"Docker版本: {result.stdout.strip()}")
                return True
            else:
                logger.error("Docker不可用")
                return False
        except FileNotFoundError:
            logger.error("Docker未安装")
            return False
    
    def check_nvidia_docker(self):
        """检查NVIDIA Docker支持"""
        try:
            result = subprocess.run(["docker", "run", "--rm", "--gpus", "all", "nvidia/cuda:11.0-base", "nvidia-smi"], 
                                  capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                logger.info("NVIDIA Docker支持正常")
                return True
            else:
                logger.warning("NVIDIA Docker支持可能有问题")
                return False
        except (FileNotFoundError, subprocess.TimeoutExpired):
            logger.warning("无法验证NVIDIA Docker支持")
            return False
    
    def pull_image(self):
        """拉取Docker镜像"""
        logger.info("正在拉取Higgs Audio vLLM镜像...")
        try:
            result = subprocess.run([
                "docker", "pull", "bosonai/higgs-audio-vllm:latest"
            ], check=True, capture_output=True, text=True)
            logger.info("镜像拉取成功")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"镜像拉取失败: {e.stderr}")
            return False
    
    def start_server(self):
        """启动vLLM服务器"""
        # 停止可能存在的容器
        self.stop_server()
        
        logger.info("正在启动vLLM Docker容器...")
        
        # 构建Docker命令
        cmd = [
            "docker", "run", "--rm", "--name", self.container_name,
            "--gpus", "all", "--ipc=host", "--shm-size=20gb", 
            "--network=host",
            "bosonai/higgs-audio-vllm:latest",
            "--served-model-name", "higgs-audio-v2-generation-3B-base",
            "--model", "bosonai/higgs-audio-v2-generation-3B-base",
            "--audio-tokenizer-type", "bosonai/higgs-audio-v2-tokenizer",
            "--limit-mm-per-prompt", "audio=50",
            "--max-model-len", "8192",
            "--port", str(self.port),
            "--gpu-memory-utilization", "0.8",
            "--disable-mm-preprocessor-cache"
        ]
        
        logger.info(f"执行命令: {' '.join(cmd)}")
        
        try:
            # 启动容器
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 等待服务器启动
            logger.info("等待服务器启动...")
            return self.wait_for_server()
            
        except Exception as e:
            logger.error(f"启动服务器失败: {e}")
            return False
    
    def wait_for_server(self, timeout=300):
        """等待服务器启动"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"http://localhost:{self.port}/health", timeout=5)
                if response.status_code == 200:
                    logger.info("vLLM服务器启动成功！")
                    return True
            except requests.exceptions.RequestException:
                pass
            
            # 检查进程是否还在运行
            if self.process and self.process.poll() is not None:
                logger.error("Docker容器意外退出")
                return False
            
            time.sleep(5)
        
        logger.error("服务器启动超时")
        return False
    
    def stop_server(self):
        """停止服务器"""
        try:
            # 停止Docker容器
            subprocess.run(["docker", "stop", self.container_name], 
                         capture_output=True, timeout=30)
            logger.info("Docker容器已停止")
        except subprocess.TimeoutExpired:
            logger.warning("强制停止Docker容器")
            subprocess.run(["docker", "kill", self.container_name], 
                         capture_output=True)
        except:
            pass
        
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=10)
            except:
                self.process.kill()
            self.process = None

def test_vllm_with_docker():
    """使用Docker测试vLLM"""
    manager = DockerVLLMManager()
    
    # 检查Docker环境
    if not manager.check_docker_available():
        logger.error("Docker环境不可用，无法运行测试")
        return False
    
    # 检查NVIDIA Docker（可选）
    manager.check_nvidia_docker()
    
    # 拉取镜像
    if not manager.pull_image():
        logger.error("无法拉取Docker镜像")
        return False
    
    # 设置信号处理
    def signal_handler(sig, frame):
        logger.info("收到中断信号，正在停止服务器...")
        manager.stop_server()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 启动服务器
        if not manager.start_server():
            logger.error("服务器启动失败")
            return False
        
        # 运行测试
        logger.info("开始运行语音合成测试...")
        
        # 创建客户端和测试器
        client = HiggsAudioVLLMClient()
        tester = VLLMTester(
            client, 
            prompt_config="chinese_natural",
            ref_audio="zh_vo_Main_Linaxita_2_3_35_6"
        )
        
        # 测试单个文本合成
        logger.info("\n=== 单个文本测试 ===")
        result = tester.synthesize_text(
            "你好，这是使用vLLM Docker进行的语音合成测试。",
            "vllm_docker_test.wav"
        )
        
        if result["success"]:
            logger.info("✓ 单个文本测试成功")
        else:
            logger.error("✗ 单个文本测试失败")
            return False
        
        # 测试批量合成
        logger.info("\n=== 批量合成测试 ===")
        test_texts = [
            "春天来了，万物复苏。",
            "夏日炎炎，绿树成荫。",
            "秋高气爽，硕果累累。"
        ]
        
        results = tester.batch_synthesize(test_texts, "vllm_docker_batch")
        successful = [r for r in results if r["success"]]
        logger.info(f"批量测试完成: {len(successful)}/{len(test_texts)} 成功")
        
        # 测试流式输出
        logger.info("\n=== 流式输出测试 ===")
        stream_result = tester.synthesize_text(
            "这是流式输出测试，应该能够更快地开始播放音频。",
            "vllm_docker_stream.wav",
            stream=True
        )
        
        if stream_result["success"]:
            logger.info("✓ 流式输出测试成功")
        else:
            logger.error("✗ 流式输出测试失败")
        
        logger.info("\n=== 测试完成 ===")
        logger.info("生成的音频文件:")
        logger.info("- vllm_docker_test.wav (单个文本)")
        logger.info("- vllm_docker_batch_*.wav (批量测试)")
        logger.info("- vllm_docker_stream.wav (流式输出)")
        
        logger.info("\nvLLM Docker测试总结:")
        logger.info("✓ Docker环境正常")
        logger.info("✓ vLLM服务器启动成功")
        logger.info("✓ 语音合成功能正常")
        logger.info("✓ 固定prompt配置生效")
        logger.info("✓ 批量处理功能正常")
        logger.info("✓ 流式输出功能正常")
        
        return True
        
    finally:
        # 清理资源
        logger.info("正在清理资源...")
        manager.stop_server()

def main():
    """主函数"""
    logger.info("开始vLLM Docker测试")
    
    success = test_vllm_with_docker()
    
    if success:
        logger.info("所有测试通过！")
    else:
        logger.error("测试失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
