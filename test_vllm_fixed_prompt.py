#!/usr/bin/env python3
"""
使用vLLM API进行语音合成测试，支持固定prompt配置
"""

import os
import time
import base64
import json
import argparse
from typing import Dict, List, Optional
import requests
import soundfile as sf
import numpy as np
from openai import OpenAI
from loguru import logger

class HiggsAudioVLLMClient:
    """Higgs Audio vLLM客户端，支持固定prompt配置"""
    
    def __init__(self, api_base: str = "http://localhost:8000/v1", api_key: str = "EMPTY"):
        self.client = OpenAI(api_key=api_key, base_url=api_base)
        self.api_base = api_base
        self.sample_rate = 24000
        
        # 获取可用模型
        try:
            models = self.client.models.list()
            self.model = models.data[0].id
            logger.info(f"使用模型: {self.model}")
        except Exception as e:
            logger.error(f"获取模型列表失败: {e}")
            self.model = "higgs-audio-v2-generation-3B-base"
    
    def check_server_status(self) -> bool:
        """检查服务器状态"""
        try:
            response = requests.get(f"{self.api_base.replace('/v1', '')}/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def encode_audio_file(self, file_path: str) -> str:
        """将音频文件编码为base64"""
        with open(file_path, "rb") as f:
            return base64.b64encode(f.read()).decode("utf-8")
    
    def setup_voice_context(self, ref_audio_name: str) -> List[Dict]:
        """设置语音上下文（参考音频）"""
        voice_dir = "examples/voice_prompts"
        audio_path = os.path.join(voice_dir, f"{ref_audio_name}.wav")
        text_path = os.path.join(voice_dir, f"{ref_audio_name}.txt")
        
        if not os.path.exists(audio_path) or not os.path.exists(text_path):
            raise FileNotFoundError(f"参考音频文件不存在: {audio_path} 或 {text_path}")
        
        with open(text_path, 'r', encoding='utf-8') as f:
            ref_text = f.read().strip()
        
        audio_base64 = self.encode_audio_file(audio_path)
        
        return [
            {"role": "user", "content": ref_text},
            {
                "role": "assistant", 
                "content": [
                    {
                        "type": "input_audio",
                        "input_audio": {
                            "data": audio_base64,
                            "format": "wav"
                        }
                    }
                ]
            }
        ]

class FixedPromptConfig:
    """固定prompt配置类"""
    
    # 预定义的系统提示配置
    SYSTEM_PROMPTS = {
        "default": {
            "name": "默认配置",
            "prompt": "Generate audio following instruction.\n\n<|scene_desc_start|>\nAudio is recorded from a quiet room.\n<|scene_desc_end|>"
        },
        "chinese_natural": {
            "name": "中文自然",
            "prompt": "Generate audio following instruction.\n\n<|scene_desc_start|>\n音频录制于安静的室内环境，说话者语调自然流畅，情感丰富，语速适中，发音清晰标准。\n<|scene_desc_end|>"
        },
        "chinese_emotional": {
            "name": "中文情感",
            "prompt": "Generate audio following instruction.\n\n<|scene_desc_start|>\n音频录制于温馨的家庭环境中，说话者心情愉悦放松，语调温和亲切，带有淡淡的幸福感，语速自然舒缓。\n<|scene_desc_end|>"
        },
        "chinese_professional": {
            "name": "中文专业",
            "prompt": "Generate audio following instruction.\n\n<|scene_desc_start|>\n音频录制于专业录音室环境，音质清晰无杂音，说话者发音标准，语调平稳，语速适中，适合正式场合使用。\n<|scene_desc_end|>"
        }
    }
    
    @classmethod
    def get_prompt(cls, config_name: str) -> str:
        """获取指定配置的prompt"""
        if config_name not in cls.SYSTEM_PROMPTS:
            logger.warning(f"未找到配置 {config_name}，使用默认配置")
            config_name = "default"
        return cls.SYSTEM_PROMPTS[config_name]["prompt"]
    
    @classmethod
    def list_configs(cls) -> Dict[str, str]:
        """列出所有可用配置"""
        return {k: v["name"] for k, v in cls.SYSTEM_PROMPTS.items()}

class VLLMTester:
    """vLLM测试器"""
    
    def __init__(self, client: HiggsAudioVLLMClient, prompt_config: str = "chinese_natural", ref_audio: str = "zh_vo_Main_Linaxita_2_3_35_6"):
        self.client = client
        self.prompt_config = prompt_config
        self.ref_audio = ref_audio
        self.system_prompt = FixedPromptConfig.get_prompt(prompt_config)
        
        # 设置语音上下文（只需要设置一次）
        self.voice_context = self.client.setup_voice_context(ref_audio)
        logger.info(f"使用prompt配置: {FixedPromptConfig.SYSTEM_PROMPTS[prompt_config]['name']}")
        logger.info(f"使用参考音频: {ref_audio}")
    
    def synthesize_text(self, text: str, output_file: Optional[str] = None, stream: bool = False) -> Dict:
        """合成单段文本"""
        if not output_file:
            timestamp = int(time.time())
            output_file = f"vllm_output_{timestamp}.wav"
        
        # 构建消息
        messages = [{"role": "system", "content": self.system_prompt}]
        messages.extend(self.voice_context)
        messages.append({"role": "user", "content": text})
        
        logger.info(f"正在合成文本: {text[:50]}...")
        start_time = time.time()
        
        try:
            chat_completion = self.client.client.chat.completions.create(
                messages=messages,
                model=self.client.model,
                modalities=["text", "audio"],
                temperature=1.0,
                top_p=0.95,
                extra_body={"top_k": 50},
                stop=["<|eot_id|>", "<|end_of_text|>", "<|audio_eos|>"],
                stream=stream
            )
            
            if stream:
                # 流式处理
                audio_data = self._handle_stream_response(chat_completion, start_time)
            else:
                # 非流式处理
                audio_data = self._handle_normal_response(chat_completion)
            
            synthesis_time = time.time() - start_time
            
            # 保存音频
            sf.write(output_file, audio_data, self.client.sample_rate)
            audio_duration = len(audio_data) / self.client.sample_rate
            
            result = {
                "text": text,
                "output_file": output_file,
                "synthesis_time": synthesis_time,
                "audio_duration": audio_duration,
                "real_time_factor": audio_duration / synthesis_time,
                "success": True
            }
            
            logger.info(f"合成完成: {output_file}")
            logger.info(f"  - 合成延迟: {synthesis_time:.2f}秒")
            logger.info(f"  - 音频时长: {audio_duration:.2f}秒")
            logger.info(f"  - 实时率: {audio_duration/synthesis_time:.2f}x")
            
            return result
            
        except Exception as e:
            logger.error(f"合成失败: {e}")
            return {
                "text": text,
                "output_file": None,
                "synthesis_time": 0,
                "audio_duration": 0,
                "real_time_factor": 0,
                "success": False,
                "error": str(e)
            }
    
    def _handle_normal_response(self, chat_completion) -> np.ndarray:
        """处理非流式响应"""
        audio_base64 = chat_completion.choices[0].message.audio.data
        audio_bytes = base64.b64decode(audio_base64)
        return np.frombuffer(audio_bytes, dtype=np.int16)
    
    def _handle_stream_response(self, chat_completion, start_time) -> np.ndarray:
        """处理流式响应"""
        from io import BytesIO
        audio_bytes_io = BytesIO()
        first_audio_latency = None
        
        for chunk in chat_completion:
            if chunk.choices and hasattr(chunk.choices[0].delta, "audio") and chunk.choices[0].delta.audio:
                if first_audio_latency is None:
                    first_audio_latency = time.time() - start_time
                    logger.info(f"首个音频块延迟: {first_audio_latency*1000:.1f}ms")
                
                audio_bytes = base64.b64decode(chunk.choices[0].delta.audio["data"])
                audio_bytes_io.write(audio_bytes)
        
        audio_bytes_io.seek(0)
        return np.frombuffer(audio_bytes_io.getvalue(), dtype=np.int16)
    
    def batch_synthesize(self, texts: List[str], output_prefix: str = "batch") -> List[Dict]:
        """批量合成文本"""
        results = []
        logger.info(f"开始批量合成 {len(texts)} 段文本")
        
        for i, text in enumerate(texts):
            output_file = f"{output_prefix}_{i+1:03d}.wav"
            result = self.synthesize_text(text, output_file)
            results.append(result)
            
            if result["success"]:
                logger.info(f"进度: {i+1}/{len(texts)} - 成功")
            else:
                logger.error(f"进度: {i+1}/{len(texts)} - 失败: {result.get('error', '未知错误')}")
        
        # 统计结果
        successful = [r for r in results if r["success"]]
        if successful:
            avg_synthesis_time = sum(r["synthesis_time"] for r in successful) / len(successful)
            avg_audio_duration = sum(r["audio_duration"] for r in successful) / len(successful)
            avg_rtf = sum(r["real_time_factor"] for r in successful) / len(successful)
            
            logger.info(f"\n批量合成完成:")
            logger.info(f"  - 成功: {len(successful)}/{len(texts)}")
            logger.info(f"  - 平均合成延迟: {avg_synthesis_time:.2f}秒")
            logger.info(f"  - 平均音频时长: {avg_audio_duration:.2f}秒")
            logger.info(f"  - 平均实时率: {avg_rtf:.2f}x")
        
        return results

def main():
    parser = argparse.ArgumentParser(description="vLLM语音合成测试工具")
    parser.add_argument("--api-base", default="http://localhost:8000/v1", help="API基础URL")
    parser.add_argument("--api-key", default="EMPTY", help="API密钥")
    parser.add_argument("--prompt-config", default="chinese_natural", 
                       choices=list(FixedPromptConfig.SYSTEM_PROMPTS.keys()),
                       help="prompt配置")
    parser.add_argument("--ref-audio", default="zh_vo_Main_Linaxita_2_3_35_6", help="参考音频名称")
    parser.add_argument("--text", help="要合成的文本")
    parser.add_argument("--text-file", help="包含多行文本的文件")
    parser.add_argument("--output", help="输出文件名")
    parser.add_argument("--stream", action="store_true", help="使用流式输出")
    parser.add_argument("--list-configs", action="store_true", help="列出所有可用的prompt配置")
    
    args = parser.parse_args()
    
    if args.list_configs:
        logger.info("可用的prompt配置:")
        for key, name in FixedPromptConfig.list_configs().items():
            logger.info(f"  {key}: {name}")
        return
    
    # 创建客户端
    client = HiggsAudioVLLMClient(args.api_base, args.api_key)
    
    # 检查服务器状态
    if not client.check_server_status():
        logger.error("vLLM服务器未启动或无法连接")
        logger.info("请先运行: python start_vllm_server.py")
        return
    
    # 创建测试器
    tester = VLLMTester(client, args.prompt_config, args.ref_audio)
    
    if args.text:
        # 单个文本合成
        result = tester.synthesize_text(args.text, args.output, args.stream)
        if not result["success"]:
            logger.error(f"合成失败: {result.get('error', '未知错误')}")
    
    elif args.text_file:
        # 批量文本合成
        if not os.path.exists(args.text_file):
            logger.error(f"文本文件不存在: {args.text_file}")
            return
        
        with open(args.text_file, 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        
        if not texts:
            logger.error("文本文件为空")
            return
        
        output_prefix = args.output or "batch_vllm"
        results = tester.batch_synthesize(texts, output_prefix)
    
    else:
        # 交互式模式
        logger.info("进入交互式模式，输入文本进行合成（输入'quit'退出）:")
        counter = 1
        while True:
            try:
                text = input("\n请输入要合成的文本: ").strip()
                if text.lower() in ['quit', 'exit', 'q']:
                    break
                if not text:
                    continue
                
                output_file = f"interactive_{counter:03d}.wav"
                result = tester.synthesize_text(text, output_file, args.stream)
                counter += 1
                
            except KeyboardInterrupt:
                logger.info("\n退出交互模式")
                break

if __name__ == "__main__":
    main()
