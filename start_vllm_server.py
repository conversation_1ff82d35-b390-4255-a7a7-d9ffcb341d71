#!/usr/bin/env python3
"""
启动vLLM服务器的脚本
"""

import subprocess
import sys
import time
import requests
from loguru import logger

def check_server_health(url="http://localhost:8000/health", timeout=300):
    """检查服务器是否启动成功"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                logger.info("vLLM服务器启动成功！")
                return True
        except requests.exceptions.RequestException:
            pass
        time.sleep(2)
    return False

def start_vllm_server():
    """启动vLLM服务器"""
    logger.info("正在启动vLLM服务器...")
    
    # vLLM启动命令
    cmd = [
        "python", "-m", "vllm.entrypoints.openai.api_server",
        "--served-model-name", "higgs-audio-v2-generation-3B-base",
        "--model", "./models/bosonai/higgs-audio-v2-generation-3B-base",
        "--audio-tokenizer-type", "./models/bosonai/higgs-audio-v2-tokenizer",
        "--limit-mm-per-prompt", "audio=50",
        "--max-model-len", "8192",
        "--port", "8000",
        "--gpu-memory-utilization", "0.8",
        "--disable-mm-preprocessor-cache",
        "--trust-remote-code"
    ]
    
    logger.info(f"执行命令: {' '.join(cmd)}")
    
    try:
        # 启动服务器进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # 实时输出日志
        logger.info("服务器启动中，请等待...")
        for line in process.stdout:
            print(line.strip())
            # 检查是否出现启动成功的标志
            if "Uvicorn running on" in line:
                logger.info("检测到服务器启动信号")
                break
        
        # 检查服务器健康状态
        if check_server_health():
            logger.info("vLLM服务器已成功启动并可以接受请求")
            logger.info("服务器地址: http://localhost:8000")
            logger.info("API文档: http://localhost:8000/docs")
            
            # 保持进程运行
            logger.info("按 Ctrl+C 停止服务器")
            try:
                process.wait()
            except KeyboardInterrupt:
                logger.info("正在停止服务器...")
                process.terminate()
                process.wait()
                logger.info("服务器已停止")
        else:
            logger.error("服务器启动失败或健康检查超时")
            process.terminate()
            return False
            
    except Exception as e:
        logger.error(f"启动服务器时出错: {e}")
        return False
    
    return True

if __name__ == "__main__":
    start_vllm_server()
