#!/usr/bin/env python3
"""
使用固定prompt配置测试语音合成（不依赖vLLM）
"""

import os
import time
import torch
import torchaudio
from typing import Dict, List, Optional
from loguru import logger
from boson_multimodal.serve.serve_engine import HiggsAudioServeEngine, HiggsAudioResponse
from boson_multimodal.data_types import ChatMLSample, Message, AudioContent

# 模型路径
MODEL_PATH = "./models/bosonai/higgs-audio-v2-generation-3B-base"
AUDIO_TOKENIZER_PATH = "./models/bosonai/higgs-audio-v2-tokenizer"
REF_AUDIO = "zh_vo_Main_Linaxita_2_3_35_6"

class FixedPromptConfig:
    """固定prompt配置类"""
    
    # 预定义的系统提示配置
    SYSTEM_PROMPTS = {
        "default": {
            "name": "默认配置",
            "prompt": "Generate audio following instruction.\n\n<|scene_desc_start|>\nAudio is recorded from a quiet room.\n<|scene_desc_end|>"
        },
        "chinese_natural": {
            "name": "中文自然",
            "prompt": "Generate audio following instruction.\n\n<|scene_desc_start|>\n音频录制于安静的室内环境，说话者语调自然流畅，情感丰富，语速适中，发音清晰标准。\n<|scene_desc_end|>"
        },
        "chinese_emotional": {
            "name": "中文情感",
            "prompt": "Generate audio following instruction.\n\n<|scene_desc_start|>\n音频录制于温馨的家庭环境中，说话者心情愉悦放松，语调温和亲切，带有淡淡的幸福感，语速自然舒缓。\n<|scene_desc_end|>"
        },
        "chinese_professional": {
            "name": "中文专业",
            "prompt": "Generate audio following instruction.\n\n<|scene_desc_start|>\n音频录制于专业录音室环境，音质清晰无杂音，说话者发音标准，语调平稳，语速适中，适合正式场合使用。\n<|scene_desc_end|>"
        },
        "chinese_storytelling": {
            "name": "中文讲故事",
            "prompt": "Generate audio following instruction.\n\n<|scene_desc_start|>\n音频录制于安静的环境中，说话者语调生动有趣，富有表现力，语速变化自然，适合讲故事和朗读。\n<|scene_desc_end|>"
        }
    }
    
    @classmethod
    def get_prompt(cls, config_name: str) -> str:
        """获取指定配置的prompt"""
        if config_name not in cls.SYSTEM_PROMPTS:
            logger.warning(f"未找到配置 {config_name}，使用默认配置")
            config_name = "default"
        return cls.SYSTEM_PROMPTS[config_name]["prompt"]
    
    @classmethod
    def list_configs(cls) -> Dict[str, str]:
        """列出所有可用配置"""
        return {k: v["name"] for k, v in cls.SYSTEM_PROMPTS.items()}

class FixedPromptTester:
    """固定prompt测试器"""
    
    def __init__(self, prompt_config: str = "chinese_natural", ref_audio: str = REF_AUDIO):
        self.prompt_config = prompt_config
        self.ref_audio = ref_audio
        self.system_prompt = FixedPromptConfig.get_prompt(prompt_config)
        self.serve_engine = None
        
        logger.info(f"使用prompt配置: {FixedPromptConfig.SYSTEM_PROMPTS[prompt_config]['name']}")
        logger.info(f"使用参考音频: {ref_audio}")
    
    def load_model(self):
        """加载模型"""
        if self.serve_engine is None:
            logger.info("正在加载模型...")
            start_time = time.time()
            
            device = "cuda" if torch.cuda.is_available() else "cpu"
            self.serve_engine = HiggsAudioServeEngine(MODEL_PATH, AUDIO_TOKENIZER_PATH, device=device)
            
            load_time = time.time() - start_time
            logger.info(f"模型加载完成，耗时: {load_time:.2f}秒")
    
    def setup_voice_context(self):
        """设置语音上下文（参考音频）"""
        voice_dir = "examples/voice_prompts"
        audio_path = os.path.join(voice_dir, f"{self.ref_audio}.wav")
        text_path = os.path.join(voice_dir, f"{self.ref_audio}.txt")
        
        if not os.path.exists(audio_path) or not os.path.exists(text_path):
            raise FileNotFoundError(f"参考音频文件不存在: {audio_path} 或 {text_path}")
        
        with open(text_path, 'r', encoding='utf-8') as f:
            ref_text = f.read().strip()
        
        return [
            Message(role="user", content=ref_text),
            Message(role="assistant", content=AudioContent(audio_url=audio_path))
        ]
    
    def synthesize_text(self, text: str, output_file: Optional[str] = None) -> Dict:
        """合成单段文本"""
        if not output_file:
            timestamp = int(time.time())
            output_file = f"fixed_prompt_output_{timestamp}.wav"
        
        # 确保模型已加载
        self.load_model()
        
        # 设置语音上下文
        voice_context = self.setup_voice_context()
        
        # 构建消息
        messages = [Message(role="system", content=self.system_prompt)]
        messages.extend(voice_context)
        messages.append(Message(role="user", content=text))
        
        logger.info(f"正在合成文本: {text[:50]}...")
        start_time = time.time()
        
        try:
            # 生成音频
            output: HiggsAudioResponse = self.serve_engine.generate(
                chat_ml_sample=ChatMLSample(messages=messages),
                max_new_tokens=1024,
                temperature=1.0,  # 使用默认温度
                top_p=0.95,
                top_k=50,
                stop_strings=["<|end_of_text|>", "<|eot_id|>"],
            )
            
            synthesis_time = time.time() - start_time
            
            # 保存音频文件
            torchaudio.save(output_file, torch.from_numpy(output.audio)[None, :], output.sampling_rate)
            audio_duration = len(output.audio) / output.sampling_rate
            
            result = {
                "text": text,
                "output_file": output_file,
                "synthesis_time": synthesis_time,
                "audio_duration": audio_duration,
                "real_time_factor": audio_duration / synthesis_time,
                "success": True
            }
            
            logger.info(f"合成完成: {output_file}")
            logger.info(f"  - 合成延迟: {synthesis_time:.2f}秒")
            logger.info(f"  - 音频时长: {audio_duration:.2f}秒")
            logger.info(f"  - 实时率: {audio_duration/synthesis_time:.2f}x")
            
            return result
            
        except Exception as e:
            logger.error(f"合成失败: {e}")
            return {
                "text": text,
                "output_file": None,
                "synthesis_time": 0,
                "audio_duration": 0,
                "real_time_factor": 0,
                "success": False,
                "error": str(e)
            }
    
    def batch_synthesize(self, texts: List[str], output_prefix: str = "batch") -> List[Dict]:
        """批量合成文本"""
        results = []
        logger.info(f"开始批量合成 {len(texts)} 段文本")
        
        for i, text in enumerate(texts):
            output_file = f"{output_prefix}_{i+1:03d}.wav"
            result = self.synthesize_text(text, output_file)
            results.append(result)
            
            if result["success"]:
                logger.info(f"进度: {i+1}/{len(texts)} - 成功")
            else:
                logger.error(f"进度: {i+1}/{len(texts)} - 失败: {result.get('error', '未知错误')}")
        
        # 统计结果
        successful = [r for r in results if r["success"]]
        if successful:
            avg_synthesis_time = sum(r["synthesis_time"] for r in successful) / len(successful)
            avg_audio_duration = sum(r["audio_duration"] for r in successful) / len(successful)
            avg_rtf = sum(r["real_time_factor"] for r in successful) / len(successful)
            
            logger.info(f"\n批量合成完成:")
            logger.info(f"  - 成功: {len(successful)}/{len(texts)}")
            logger.info(f"  - 平均合成延迟: {avg_synthesis_time:.2f}秒")
            logger.info(f"  - 平均音频时长: {avg_audio_duration:.2f}秒")
            logger.info(f"  - 平均实时率: {avg_rtf:.2f}x")
        
        return results

def test_different_prompts():
    """测试不同的prompt配置"""
    test_text = "今天是个美好的日子，阳光明媚，鸟儿在枝头歌唱，微风轻抚着大地。"
    
    results = []
    
    for config_name, config_info in FixedPromptConfig.SYSTEM_PROMPTS.items():
        logger.info(f"\n=== 测试配置: {config_info['name']} ===")
        
        tester = FixedPromptTester(prompt_config=config_name)
        result = tester.synthesize_text(test_text, f"prompt_test_{config_name}.wav")
        
        if result["success"]:
            results.append({
                "config": config_name,
                "name": config_info['name'],
                "synthesis_time": result["synthesis_time"],
                "audio_duration": result["audio_duration"],
                "real_time_factor": result["real_time_factor"],
                "output_file": result["output_file"]
            })
        else:
            logger.error(f"配置 {config_name} 测试失败")
    
    # 输出对比结果
    if results:
        logger.info("\n" + "="*80)
        logger.info("不同Prompt配置对比结果")
        logger.info("="*80)
        
        for result in results:
            logger.info(f"{result['name']:>15}: "
                       f"延迟={result['synthesis_time']:.2f}s, "
                       f"时长={result['audio_duration']:.2f}s, "
                       f"RTF={result['real_time_factor']:.2f}x")
        
        logger.info(f"\n生成的音频文件:")
        for result in results:
            logger.info(f"  - {result['output_file']} ({result['name']})")

def main():
    """主函数"""
    logger.info("开始固定Prompt配置测试")
    
    # 测试不同的prompt配置
    test_different_prompts()
    
    # 测试批量合成
    logger.info("\n" + "="*80)
    logger.info("批量合成测试")
    logger.info("="*80)
    
    tester = FixedPromptTester(prompt_config="chinese_natural")
    
    batch_texts = [
        "春天来了，万物复苏，花儿开始绽放。",
        "夏日炎炎，绿树成荫，蝉鸣声声。",
        "秋高气爽，硕果累累，金桂飘香。",
        "冬雪纷飞，银装素裹，静谧安详。"
    ]
    
    batch_results = tester.batch_synthesize(batch_texts, "fixed_prompt_batch")
    
    logger.info("\n=== 测试完成 ===")
    logger.info("固定Prompt配置测试总结:")
    logger.info("✓ 多种prompt配置测试完成")
    logger.info("✓ 批量合成功能正常")
    logger.info("✓ 语音合成延迟测量完成")
    logger.info("✓ 实现了类似vLLM的固定prompt功能")

if __name__ == "__main__":
    main()
