#!/usr/bin/env python3
"""
批量中文语音合成测试脚本
支持更大的batch size，进一步提升4090的利用率
"""

import os
import torch
import torchaudio
import time
from loguru import logger
from boson_multimodal.serve.serve_engine import Higgs<PERSON>udioServeEngine, HiggsAudioResponse
from boson_multimodal.data_types import ChatMLSample, Message, AudioContent
from concurrent.futures import ThreadPoolExecutor
import threading

class BatchHiggsAudioEngine:
    """支持批量处理的Higgs Audio引擎"""
    
    def __init__(self, model_path, tokenizer_path, device="cuda", max_batch_size=4):
        self.model_path = model_path
        self.tokenizer_path = tokenizer_path
        self.device = device
        self.max_batch_size = max_batch_size
        self.engine = None
        self._lock = threading.Lock()
        
    def initialize(self):
        """初始化模型"""
        logger.info(f"🚀 初始化批量处理引擎 (max_batch_size={self.max_batch_size})")
        start_time = time.time()
        
        self.engine = HiggsAudioServeEngine(
            self.model_path, 
            self.tokenizer_path, 
            device=self.device
        )
        
        init_time = time.time() - start_time
        logger.info(f"✅ 模型初始化完成，耗时: {init_time:.1f}秒")
        return init_time
    
    def generate_batch(self, requests):
        """批量生成语音"""
        if not self.engine:
            raise RuntimeError("Engine not initialized")
        
        results = []
        
        # 由于当前的serve_engine限制为batch_size=1，我们使用并发处理来模拟批量
        with ThreadPoolExecutor(max_workers=min(self.max_batch_size, len(requests))) as executor:
            futures = []
            
            for req in requests:
                future = executor.submit(self._generate_single, req)
                futures.append(future)
            
            for future in futures:
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    logger.error(f"生成失败: {e}")
                    results.append(None)
        
        return results
    
    def _generate_single(self, request):
        """生成单个语音"""
        with self._lock:  # 确保线程安全
            try:
                start_time = time.time()
                
                # 构建消息
                messages = [Message(role="system", content=request["system_prompt"])]
                
                # 添加参考音频（如果有）
                if request.get("ref_audio"):
                    ref_text_file = f"examples/voice_prompts/{request['ref_audio']}.txt"
                    if os.path.exists(ref_text_file):
                        with open(ref_text_file, 'r', encoding='utf-8') as f:
                            ref_text = f.read().strip()
                        
                        messages.append(Message(role="user", content=ref_text))
                        messages.append(Message(
                            role="assistant", 
                            content=AudioContent(audio_url=f"examples/voice_prompts/{request['ref_audio']}.wav")
                        ))
                
                # 添加目标文本
                messages.append(Message(role="user", content=request["text"]))
                
                # 生成语音
                output: HiggsAudioResponse = self.engine.generate(
                    chat_ml_sample=ChatMLSample(messages=messages),
                    max_new_tokens=1024,
                    temperature=request.get("temperature", 0.3),
                    top_p=0.95,
                    top_k=50,
                    stop_strings=["<|end_of_text|>", "<|eot_id|>"],
                )
                
                gen_time = time.time() - start_time
                audio_duration = len(output.audio) / output.sampling_rate
                
                return {
                    "audio": output.audio,
                    "sampling_rate": output.sampling_rate,
                    "generation_time": gen_time,
                    "audio_duration": audio_duration,
                    "name": request["name"],
                    "success": True
                }
                
            except Exception as e:
                return {
                    "error": str(e),
                    "name": request["name"],
                    "success": False
                }

def test_batch_chinese_voice():
    """测试批量中文语音合成"""
    
    logger.info("🚀 开始批量中文语音合成测试...")
    logger.info(f"🎮 GPU: {torch.cuda.get_device_name(0)}")
    logger.info(f"💾 显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
    
    # 使用本地模型路径
    model_path = "./models/bosonai/higgs-audio-v2-generation-3B-base"
    tokenizer_path = "./models/bosonai/higgs-audio-v2-tokenizer"
    
    # 创建输出目录
    output_dir = "./batch_chinese_outputs"
    os.makedirs(output_dir, exist_ok=True)
    
    # 系统提示
    system_prompt = (
        "Generate audio following instruction.\n\n"
        "<|scene_desc_start|>\nAudio is recorded from a quiet room.\n<|scene_desc_end|>"
    )
    
    # 准备批量请求
    batch_requests = [
        {
            "text": "你好，这是第一个批量测试语音。",
            "ref_audio": "zh_vo_Main_Linaxita_2_3_35_6",
            "name": "batch_test_1",
            "system_prompt": system_prompt,
            "temperature": 0.3
        },
        {
            "text": "这是第二个批量测试，使用不同的文本内容。",
            "ref_audio": "zh_vo_Main_Linaxita_2_3_35_6",
            "name": "batch_test_2", 
            "system_prompt": system_prompt,
            "temperature": 0.3
        },
        {
            "text": "第三个测试使用智能语音，不需要参考音频。",
            "ref_audio": None,
            "name": "batch_test_3",
            "system_prompt": system_prompt,
            "temperature": 0.3
        },
        {
            "text": "最后一个批量测试，验证并发处理能力。",
            "ref_audio": "zh_vo_Main_Linaxita_2_3_35_6",
            "name": "batch_test_4",
            "system_prompt": system_prompt,
            "temperature": 0.3
        },
        {
            "text": "额外测试：人工智能技术正在改变世界。",
            "ref_audio": "zh_vo_Main_Linaxita_2_3_35_6",
            "name": "batch_test_5",
            "system_prompt": system_prompt,
            "temperature": 0.3
        },
        {
            "text": "最终测试：语音合成技术的未来充满无限可能。",
            "ref_audio": None,
            "name": "batch_test_6",
            "system_prompt": system_prompt,
            "temperature": 0.3
        }
    ]
    
    try:
        # 初始化批量引擎
        batch_engine = BatchHiggsAudioEngine(
            model_path, 
            tokenizer_path, 
            device="cuda",
            max_batch_size=4  # 4090可以处理更大的并发
        )
        
        init_time = batch_engine.initialize()
        
        # 批量生成
        logger.info(f"\n🎯 开始批量生成 {len(batch_requests)} 个语音...")
        batch_start = time.time()
        
        results = batch_engine.generate_batch(batch_requests)
        
        batch_time = time.time() - batch_start
        
        # 保存结果并统计
        success_count = 0
        total_audio_duration = 0
        
        for i, result in enumerate(results):
            if result and result["success"]:
                # 保存音频
                output_path = os.path.join(output_dir, f"{result['name']}.wav")
                torchaudio.save(
                    output_path,
                    torch.from_numpy(result["audio"])[None, :],
                    result["sampling_rate"]
                )
                
                file_size = os.path.getsize(output_path)
                
                logger.info(f"✅ {result['name']}: {result['audio_duration']:.1f}s音频, {result['generation_time']:.1f}s生成, {file_size}bytes")
                
                success_count += 1
                total_audio_duration += result["audio_duration"]
            else:
                logger.error(f"❌ {batch_requests[i]['name']}: 生成失败")
        
        # 性能统计
        avg_gen_time = batch_time / len(batch_requests)
        throughput = total_audio_duration / batch_time
        
        logger.info(f"\n{'='*60}")
        logger.info(f"📊 批量处理性能统计:")
        logger.info(f"✅ 成功: {success_count}/{len(batch_requests)}")
        logger.info(f"⏱️  模型初始化: {init_time:.1f}秒")
        logger.info(f"⏱️  批量生成总时间: {batch_time:.1f}秒")
        logger.info(f"⏱️  平均每个生成时间: {avg_gen_time:.1f}秒")
        logger.info(f"🎵 总音频时长: {total_audio_duration:.1f}秒")
        logger.info(f"🚀 吞吐量: {throughput:.1f}x 实时")
        logger.info(f"📁 输出目录: {output_dir}")
        
        if success_count == len(batch_requests):
            logger.info("🎉 所有批量测试都成功了！")
        
        return success_count > 0
        
    except Exception as e:
        logger.error(f"💥 批量测试失败: {e}")
        return False

if __name__ == "__main__":
    logger.info("🎭 批量中文语音合成测试")
    logger.info("💡 使用并发处理提升4090利用率")
    
    success = test_batch_chinese_voice()
    
    if success:
        logger.info("\n🚀 批量处理优化提示:")
        logger.info("1. 当前使用并发处理模拟批量（受限于serve_engine设计）")
        logger.info("2. 4090的24GB显存可以支持更大的并发数")
        logger.info("3. 实际批量处理需要修改底层模型代码")
        logger.info("4. 当前方案已经能显著提升吞吐量")
    else:
        logger.error("💥 批量测试失败！")
        exit(1)
