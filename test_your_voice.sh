#!/bin/bash
# 测试你的自定义声音克隆

echo "🎭 测试你的自定义声音克隆..."

# 创建输出目录
mkdir -p chinese_voice_tests

# 测试1: 基础测试
echo "📝 测试1: 基础中文语音克隆"
./conda_env/bin/python examples/generation.py \
  --transcript "你好，这是使用你的声音进行语音克隆的测试。" \
  --ref_audio zh_vo_Main_Linaxita_2_3_35_6 \
  --seed 12345 \
  --temperature 0.3 \
  --out_path chinese_voice_tests/test1_basic.wav

# 测试2: 技术内容
echo "📝 测试2: 技术内容"
./conda_env/bin/python examples/generation.py \
  --transcript "人工智能技术正在快速发展，语音合成效果越来越自然。" \
  --ref_audio zh_vo_Main_Linaxita_2_3_35_6 \
  --seed 12345 \
  --temperature 0.3 \
  --out_path chinese_voice_tests/test2_tech.wav

# 测试3: 日常对话
echo "📝 测试3: 日常对话"
./conda_env/bin/python examples/generation.py \
  --transcript "今天天气很好，阳光明媚，心情也很不错。" \
  --ref_audio zh_vo_Main_Linaxita_2_3_35_6 \
  --seed 12345 \
  --temperature 0.3 \
  --out_path chinese_voice_tests/test3_daily.wav

echo "✅ 测试完成！音频文件保存在 chinese_voice_tests/ 目录"
echo "💡 你可以播放这些wav文件来评估语音克隆效果"
